﻿@model PHONE_STORE.WEB.Models.CustomerFormVm
@{
    ViewData["Title"] = "Create Customer";
}
<h1>@ViewData["Title"]</h1>

<form method="post">
    @Html.AntiForgeryToken()
    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

    <div class="row g-3">
        <div class="col-md-3">
            <label class="form-label">UserAccountId</label>
            <input asp-for="UserAccountId" class="form-control" />
            <span asp-validation-for="UserAccountId" class="text-danger"></span>
        </div>
        <div class="col-md-3">
            <label class="form-label">Full name</label>
            <input asp-for="FullName" class="form-control" />
            <span asp-validation-for="FullName" class="text-danger"></span>
        </div>
        <div class="col-md-3">
            <label class="form-label">Email</label>
            <input asp-for="Email" class="form-control" />
            <span asp-validation-for="Email" class="text-danger"></span>
        </div>
        <div class="col-md-3">
            <label class="form-label">Phone</label>
            <input asp-for="Phone" class="form-control" />
            <span asp-validation-for="Phone" class="text-danger"></span>
        </div>
    </div>

    <div class="mt-3">
        <button class="btn btn-primary">Save</button>
        <a class="btn btn-link" asp-action="Index">Back</a>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
