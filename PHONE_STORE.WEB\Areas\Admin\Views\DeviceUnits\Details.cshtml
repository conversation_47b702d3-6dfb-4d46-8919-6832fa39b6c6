﻿@model PHONE_STORE.Application.Dtos.DeviceUnitDto
@{
    ViewData["Title"] = "Device Unit Details";
}
<h1>Device Unit</h1>

<dl class="row">
    <dt class="col-sm-3">IMEI</dt>
    <dd class="col-sm-9">@Model.Imei</dd>
    <dt class="col-sm-3">Variant</dt>
    <dd class="col-sm-9">@Model.VariantId</dd>
    <dt class="col-sm-3">Status</dt>
    <dd class="col-sm-9">@Model.Status</dd>
    <dt class="col-sm-3">Warehouse</dt>
    <dd class="col-sm-9">@Model.WarehouseId</dd>
    <dt class="col-sm-3">Received</dt>
    <dd class="col-sm-9">@Model.ReceivedAt.ToLocalTime()</dd>
    <dt class="col-sm-3">Sold</dt>
    <dd class="col-sm-9">@(@Model.SoldAt?.ToLocalTime().ToString() ?? "-")</dd>
    <dt class="col-sm-3">Returned</dt>
    <dd class="col-sm-9">@(@Model.ReturnedAt?.ToLocalTime().ToString() ?? "-")</dd>
</dl>

<a class="btn btn-secondary" asp-area="Admin" asp-controller="DeviceUnits" asp-action="Index">Back</a>
