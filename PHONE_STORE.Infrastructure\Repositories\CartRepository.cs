﻿using Microsoft.EntityFrameworkCore;
using PHONE_STORE.Application.Dtos;
using PHONE_STORE.Application.Interfaces;
using PHONE_STORE.Infrastructure.Data;
using PHONE_STORE.Infrastructure.Entities;

namespace PHONE_STORE.Infrastructure.Repositories;

public class CartRepository : ICartRepository
{
    private readonly PhoneDbContext _db;
    public CartRepository(PhoneDbContext db) => _db = db;

    // Giá hiện tại của 1 variant (SalePrice ?? ListPrice) tại thời điểm now
    private static decimal NowPriceForVariant(PhoneDbContext db, long variantId)
    {
        var now = DateTime.UtcNow;
        var p = db.ProductPrices.AsNoTracking()
            .Where(x => x.VariantId == variantId && x.StartsAt <= now && (x.EndsAt == null || x.EndsAt > now))
            .OrderByDescending(x => x.StartsAt)
            .Select(x => x.SalePrice ?? x.ListPrice)
            .FirstOrDefault();

        return p;
    }

    private async Task<long> EnsureCartId(long? customerId, string? sessionId, CancellationToken ct)
    {
        // Tìm cart theo customer hoặc session_token
        var q = _db.Carts.AsQueryable();
        var cart =
            customerId.HasValue && customerId > 0
                ? await q.FirstOrDefaultAsync(x => x.CustomerId == customerId, ct)
                : !string.IsNullOrWhiteSpace(sessionId)
                    ? await q.FirstOrDefaultAsync(x => x.SessionId == sessionId, ct)
                    : null;

        if (cart is null)
        {
            cart = new Infrastructure.Entities.Cart
            {
                CustomerId = customerId,
                SessionId = customerId.HasValue ? null : sessionId,
                Currency = "VND",
                Status = "ACTIVE",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            _db.Carts.Add(cart);
            await _db.SaveChangesAsync(ct);
        }
        return cart.Id;
    }

    private static CartDto ToDto(
        Infrastructure.Entities.Cart cart,
        List<(long VariantId, string Sku, string? Color, int? StorageGb, decimal UnitPrice, int Qty)> lines)
    {
        var items = lines
            .Select(l => new CartItemDto(
                Id: l.VariantId,               // PK của CART_ITEMS là (CART_ID, VARIANT_ID) → dùng VariantId làm "itemId"
                VariantId: l.VariantId,
                Sku: l.Sku,
                Color: l.Color,
                StorageGb: l.StorageGb,
                UnitPrice: l.UnitPrice,
                Quantity: l.Qty,
                LineTotal: l.UnitPrice * l.Qty))
            .ToList();

        var subtotal = items.Sum(i => i.LineTotal);
        return new CartDto(cart.Id, cart.CustomerId, cart.SessionId, items, subtotal, "VND");
    }

    public async Task<CartDto> GetOrCreateAsync(long? customerId, string? sessionId, CancellationToken ct)
    {
        var id = await EnsureCartId(customerId, sessionId, ct);
        var cart = await _db.Carts.AsNoTracking().FirstAsync(x => x.Id == id, ct);

        // join CART_ITEMS + VARIANTS để lấy hiển thị
        var rows = await
            (from ci in _db.CartItems.AsNoTracking().Where(x => x.CartId == id)
             join v in _db.ProductVariants.AsNoTracking() on ci.VariantId equals v.Id
             select new { ci, v })
            .ToListAsync(ct);

        var mapped = rows.Select(x =>
        {
            var unit = x.ci.UnitPrice ?? NowPriceForVariant(_db, x.ci.VariantId);
            return (x.v.Id, x.v.Sku, x.v.Color, x.v.StorageGb, unit, x.ci.Quantity);
        }).ToList();

        return ToDto(cart, mapped);
    }

    public async Task<CartDto> AddOrUpdateItemAsync(long? customerId, string? sessionId, CartItemUpsertDto dto, CancellationToken ct)
    {
        var id = await EnsureCartId(customerId, sessionId, ct);

        var item = await _db.CartItems
            .FirstOrDefaultAsync(x => x.CartId == id && x.VariantId == dto.VariantId, ct);

        if (item is null)
        {
            var price = NowPriceForVariant(_db, dto.VariantId);
            item = new Infrastructure.Entities.CartItem
            {
                CartId = id,
                VariantId = dto.VariantId,
                Quantity = Math.Max(1, dto.Quantity),
                UnitPrice = price,               // snapshot tại thời điểm add
                Currency = "VND",
                AddedAt = DateTime.UtcNow
            };
            _db.CartItems.Add(item);
        }
        else
        {
            item.Quantity += dto.Quantity;
            if (item.Quantity < 1) item.Quantity = 1;
        }

        await _db.SaveChangesAsync(ct);
        return await GetOrCreateAsync(customerId, sessionId, ct);
    }

    public async Task<CartDto> UpdateQtyAsync(long itemId, int qty, long? customerId, string? sessionId, CancellationToken ct)
    {
        var id = await EnsureCartId(customerId, sessionId, ct);

        // itemId = VariantId (vì PK kép)
        var item = await _db.CartItems.FirstOrDefaultAsync(x => x.CartId == id && x.VariantId == itemId, ct);
        if (item == null) return await GetOrCreateAsync(customerId, sessionId, ct);

        if (qty <= 0) _db.CartItems.Remove(item);
        else item.Quantity = qty;

        await _db.SaveChangesAsync(ct);
        return await GetOrCreateAsync(customerId, sessionId, ct);
    }

    public async Task RemoveItemAsync(long itemId, long? customerId, string? sessionId, CancellationToken ct)
    {
        var id = await EnsureCartId(customerId, sessionId, ct);
        var item = await _db.CartItems.FirstOrDefaultAsync(x => x.CartId == id && x.VariantId == itemId, ct);
        if (item != null)
        {
            _db.CartItems.Remove(item);
            await _db.SaveChangesAsync(ct);
        }
    }

    public async Task MergeAsync(string sessionId, long customerId, CancellationToken ct)
    {
        var guest = await _db.Carts.Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.SessionId == sessionId, ct);

        var user = await _db.Carts.Include(c => c.Items)
            .FirstOrDefaultAsync(c => c.CustomerId == customerId, ct);

        if (guest == null) return;

        if (user == null)
        {
            user = new Infrastructure.Entities.Cart
            {
                CustomerId = customerId,
                Currency = "VND",
                Status = "ACTIVE",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            _db.Carts.Add(user);
        }

        foreach (var gi in guest.Items)
        {
            var ui = user.Items.FirstOrDefault(x => x.VariantId == gi.VariantId);
            if (ui == null)
            {
                user.Items.Add(new Infrastructure.Entities.CartItem
                {
                    VariantId = gi.VariantId,
                    Quantity = gi.Quantity,
                    UnitPrice = gi.UnitPrice,
                    Currency = gi.Currency,
                    AddedAt = DateTime.UtcNow
                });
            }
            else
            {
                ui.Quantity += gi.Quantity;
            }
        }

        _db.Carts.Remove(guest);
        await _db.SaveChangesAsync(ct);
    }

    public async Task ClearAsync(long? customerId, string? sessionId, CancellationToken ct)
    {
        var id = await EnsureCartId(customerId, sessionId, ct);
        var items = _db.CartItems.Where(x => x.CartId == id);
        _db.CartItems.RemoveRange(items);
        await _db.SaveChangesAsync(ct);
    }
}
