{"openapi": "3.0.1", "info": {"title": "PHONE_STORE.API", "version": "1.0"}, "paths": {"/api/admin/attributes": {"get": {"tags": ["Attributes"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AttributeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AttributeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AttributeDto"}}}}}}}, "post": {"tags": ["Attributes"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttributeCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AttributeCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AttributeCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/attributes/{id}": {"put": {"tags": ["Attributes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttributeUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AttributeUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AttributeUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Attributes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/admin/brands": {"get": {"tags": ["Brands"], "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BrandDtoPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BrandDtoPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BrandDtoPagedResult"}}}}}}, "post": {"tags": ["Brands"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BrandCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BrandCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BrandCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/brands/{id}": {"get": {"tags": ["Brands"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BrandDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BrandDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BrandDto"}}}}}}, "put": {"tags": ["Brands"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BrandUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BrandUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BrandUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Brands"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/brands/options": {"get": {"tags": ["Brands"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IdNameDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IdNameDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IdNameDto"}}}}}}}}, "/api/admin/categories": {"get": {"tags": ["Categories"], "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDtoPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoPagedResult"}}}}}}, "post": {"tags": ["Categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategoryCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/categories/{id}": {"get": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDto"}}}}}}, "put": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategoryUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/categories/options": {"get": {"tags": ["Categories"], "parameters": [{"name": "excludeId", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/customers": {"get": {"tags": ["Customers"], "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "top", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDto"}}}}}}}, "post": {"tags": ["Customers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/customers/{id}": {"get": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CustomerDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CustomerDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerDto"}}}}}}, "put": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/customers/{id}/addresses": {"get": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDto"}}}}}}}, "post": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddressUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/customers/{id}/addresses/{addressId}": {"get": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "addressId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AddressDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddressDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressDto"}}}}}}, "put": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "addressId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddressUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "addressId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/customers/{id}/addresses/{addressId}/set-default": {"post": {"tags": ["Customers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "addressId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "type", "in": "query", "schema": {"type": "string", "default": "SHIPPING"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/device-units": {"get": {"tags": ["DeviceUnits"], "parameters": [{"name": "imei", "in": "query", "schema": {"type": "string"}}, {"name": "variantId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "warehouseId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "top", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceUnitDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceUnitDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceUnitDto"}}}}}}}}, "/api/admin/device-units/{id}": {"get": {"tags": ["DeviceUnits"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeviceUnitDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeviceUnitDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeviceUnitDto"}}}}}}}, "/api/admin/images/product/{productId}": {"get": {"tags": ["Images"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}}}}}}}}, "/api/admin/images/variant/{variantId}": {"get": {"tags": ["Images"], "parameters": [{"name": "variantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}}}}}}}}, "/api/admin/images": {"post": {"tags": ["Images"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ImageCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ImageCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/images/{id}": {"delete": {"tags": ["Images"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/inventory": {"get": {"tags": ["Inventory"], "parameters": [{"name": "variantId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "warehouseId", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/inventory/move": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockMoveRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockMoveRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockMoveRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/orders": {"get": {"tags": ["Orders"], "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "top", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderListItemDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderListItemDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderListItemDto"}}}}}}}}, "/api/admin/orders/{id}": {"get": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderDetailDto"}}}}}}}, "/api/admin/orders/{id}/mark-paid": {"post": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/orders/{id}/ship": {"post": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/orders/{id}/cancel": {"post": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/variants/{variantId}/prices": {"get": {"tags": ["Prices"], "parameters": [{"name": "variantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PriceDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PriceDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PriceDto"}}}}}}}, "post": {"tags": ["Prices"], "parameters": [{"name": "variantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PriceUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PriceUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/variants/{variantId}/prices/active": {"get": {"tags": ["Prices"], "parameters": [{"name": "variantId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "at", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PriceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PriceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PriceDto"}}}}}}}, "/api/admin/variants/{variantId}/prices/{id}": {"put": {"tags": ["Prices"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "variantId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PriceUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PriceUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Prices"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "variantId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/products/{productId}/attributes": {"get": {"tags": ["ProductAttributes"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductAttrValueDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductAttrValueDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductAttrValueDto"}}}}}}}}, "/api/admin/products/{productId}/attributes/upsert": {"post": {"tags": ["ProductAttributes"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttributeValueUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AttributeValueUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AttributeValueUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/products": {"get": {"tags": ["Products"], "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "brandId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductListItemDtoPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductListItemDtoPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductListItemDtoPagedResult"}}}}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/shop/cart": {"get": {"tags": ["ShopCart"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDto"}}}}}}}, "/api/shop/cart/items": {"post": {"tags": ["ShopCart"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartItemUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartItemUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CartItemUpsertDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDto"}}}}}}}, "/api/shop/cart/items/{itemId}": {"put": {"tags": ["ShopCart"], "parameters": [{"name": "itemId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "qty", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CartDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CartDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartDto"}}}}}}, "delete": {"tags": ["ShopCart"], "parameters": [{"name": "itemId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}}}}, "/api/shop/cart/merge": {"post": {"tags": ["ShopCart"], "responses": {"200": {"description": "OK"}}}}, "/api/shop/checkout/preview": {"post": {"tags": ["ShopCheckout"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutPreviewRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckoutPreviewRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CheckoutPreviewRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CheckoutPreviewResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CheckoutPreviewResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckoutPreviewResult"}}}}}}}, "/api/shop/checkout/submit": {"post": {"tags": ["ShopCheckout"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSubmitRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckoutSubmitRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CheckoutSubmitRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/shop/me/addresses": {"get": {"tags": ["ShopMe"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDto"}}}}}}}}, "/api/shop/products": {"get": {"tags": ["ShopProducts"], "parameters": [{"name": "q", "in": "query", "schema": {"type": "string"}}, {"name": "catId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShopProductListItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShopProductListItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShopProductListItem"}}}}}}}}, "/api/shop/products/{slug}": {"get": {"tags": ["ShopProducts"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ShopProductDetail"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ShopProductDetail"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShopProductDetail"}}}}}}}, "/api/admin/products/{productId}/variants": {"get": {"tags": ["Variants"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VariantDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VariantDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VariantDto"}}}}}}}, "post": {"tags": ["Variants"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariantCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VariantCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VariantCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/products/{productId}/variants/{id}": {"get": {"tags": ["Variants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VariantDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VariantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VariantDto"}}}}}}, "put": {"tags": ["Variants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VariantUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VariantUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VariantUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Variants"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/warehouses": {"get": {"tags": ["Warehouses"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseDto"}}}}}}}, "post": {"tags": ["Warehouses"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/warehouses/{id}": {"get": {"tags": ["Warehouses"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WarehouseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WarehouseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseDto"}}}}}}, "put": {"tags": ["Warehouses"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WarehouseUpsertDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WarehouseUpsertDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WarehouseUpsertDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/warehouses/options": {"get": {"tags": ["Warehouses"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseOptionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseOptionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WarehouseOptionDto"}}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"AddressDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "customerId": {"type": "integer", "format": "int64"}, "label": {"type": "string", "nullable": true}, "recipient": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "line1": {"type": "string", "nullable": true}, "ward": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "addressType": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "AddressUpsertDto": {"type": "object", "properties": {"label": {"type": "string", "nullable": true}, "recipient": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "line1": {"type": "string", "nullable": true}, "ward": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "addressType": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}}, "additionalProperties": false}, "AttributeCreateDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "dataType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AttributeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "dataType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AttributeUpdateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "dataType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AttributeValueUpsertDto": {"type": "object", "properties": {"attributeId": {"type": "integer", "format": "int64"}, "intValue": {"type": "integer", "format": "int64", "nullable": true}, "decValue": {"type": "number", "format": "double", "nullable": true}, "boolValue": {"type": "boolean", "nullable": true}, "textValue": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BrandCreateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "BrandDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "BrandDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/BrandDto"}, "nullable": true}, "total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BrandUpdateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CartDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "customerId": {"type": "integer", "format": "int64", "nullable": true}, "sessionId": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CartItemDto"}, "nullable": true}, "subtotal": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CartItemDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "variantId": {"type": "integer", "format": "int64"}, "sku": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "storageGb": {"type": "integer", "format": "int32", "nullable": true}, "unitPrice": {"type": "number", "format": "double"}, "quantity": {"type": "integer", "format": "int32"}, "lineTotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "CartItemUpsertDto": {"type": "object", "properties": {"variantId": {"type": "integer", "format": "int64"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategoryCreateDto": {"type": "object", "properties": {"parentId": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CategoryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "parentId": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CategoryDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}, "nullable": true}, "total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategoryUpdateDto": {"type": "object", "properties": {"parentId": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ChangePasswordRequest": {"type": "object", "properties": {"oldPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "currentRefreshTokenJti": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CheckoutPreviewItem": {"type": "object", "properties": {"variantId": {"type": "integer", "format": "int64"}, "title": {"type": "string", "nullable": true}, "sku": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "lineTotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "CheckoutPreviewRequest": {"type": "object", "properties": {"addressId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "CheckoutPreviewResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CheckoutPreviewItem"}, "nullable": true}, "subtotal": {"type": "number", "format": "double"}, "taxTotal": {"type": "number", "format": "double"}, "shippingFee": {"type": "number", "format": "double"}, "discountTotal": {"type": "number", "format": "double"}, "grandTotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "CheckoutSubmitRequest": {"type": "object", "properties": {"addressId": {"type": "integer", "format": "int64"}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CustomerDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userAccountId": {"type": "integer", "format": "int64", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CustomerUpsertDto": {"type": "object", "properties": {"userAccountId": {"type": "integer", "format": "int64", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeviceUnitDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "variantId": {"type": "integer", "format": "int64"}, "imei": {"type": "string", "nullable": true}, "serialNo": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "warehouseId": {"type": "integer", "format": "int64", "nullable": true}, "receivedAt": {"type": "string", "format": "date-time"}, "soldAt": {"type": "string", "format": "date-time", "nullable": true}, "returnedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ForgotPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IdNameDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ImageCreateDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int64", "nullable": true}, "variantId": {"type": "integer", "format": "int64", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "altText": {"type": "string", "nullable": true}, "isPrimary": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ImageDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "productId": {"type": "integer", "format": "int64", "nullable": true}, "variantId": {"type": "integer", "format": "int64", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "altText": {"type": "string", "nullable": true}, "isPrimary": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "customerId": {"type": "integer", "format": "int64", "nullable": true}, "shippingAddressId": {"type": "integer", "format": "int64", "nullable": true}, "subtotal": {"type": "number", "format": "double"}, "discountTotal": {"type": "number", "format": "double"}, "taxTotal": {"type": "number", "format": "double"}, "shippingFee": {"type": "number", "format": "double"}, "grandTotal": {"type": "number", "format": "double"}, "note": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "placedAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItemLineDto"}, "nullable": true}}, "additionalProperties": false}, "OrderItemLineDto": {"type": "object", "properties": {"variantId": {"type": "integer", "format": "int64"}, "productName": {"type": "string", "nullable": true}, "sku": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "lineTotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "OrderListItemDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "customerEmail": {"type": "string", "nullable": true}, "grandTotal": {"type": "number", "format": "double"}, "placedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PriceDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "variantId": {"type": "integer", "format": "int64"}, "listPrice": {"type": "number", "format": "double"}, "salePrice": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "startsAt": {"type": "string", "format": "date-time"}, "endsAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "PriceUpsertDto": {"type": "object", "properties": {"variantId": {"type": "integer", "format": "int64"}, "listPrice": {"type": "number", "format": "double"}, "salePrice": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "startsAt": {"type": "string", "format": "date-time"}, "endsAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ProductAttrValueDto": {"type": "object", "properties": {"attr": {"$ref": "#/components/schemas/AttributeDto"}, "value": {"nullable": true}}, "additionalProperties": false}, "ProductCreateDto": {"type": "object", "properties": {"brandId": {"type": "integer", "format": "int64"}, "defaultCategoryId": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "specJson": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ProductDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "brandId": {"type": "integer", "format": "int64"}, "defaultCategoryId": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "specJson": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ProductListItemDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "brandName": {"type": "string", "nullable": true}, "categoryName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "activeVariantCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductListItemDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ProductListItemDto"}, "nullable": true}, "total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductUpdateDto": {"type": "object", "properties": {"brandId": {"type": "integer", "format": "int64"}, "defaultCategoryId": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "specJson": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "RefreshRequest": {"type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ShopImage": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "url": {"type": "string", "nullable": true}, "alt": {"type": "string", "nullable": true}, "sort": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ShopProductDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "variants": {"type": "array", "items": {"$ref": "#/components/schemas/ShopVariant"}, "nullable": true}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ShopImage"}, "nullable": true}}, "additionalProperties": false}, "ShopProductListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "minPrice": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ShopVariant": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "sku": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "storageGb": {"type": "integer", "format": "int32", "nullable": true}, "price": {"type": "number", "format": "double"}}, "additionalProperties": false}, "StockMoveRequestDto": {"type": "object", "properties": {"variantId": {"type": "integer", "format": "int64"}, "warehouseId": {"type": "integer", "format": "int64"}, "movementType": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "refType": {"type": "string", "nullable": true}, "refId": {"type": "integer", "format": "int64", "nullable": true}, "refCode": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "imeiList": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "VariantCreateDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int64"}, "sku": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "storageGb": {"type": "integer", "format": "int32", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "weightGram": {"type": "number", "format": "double", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "VariantDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "productId": {"type": "integer", "format": "int64"}, "sku": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "storageGb": {"type": "integer", "format": "int32", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "weightGram": {"type": "number", "format": "double", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "VariantUpdateDto": {"type": "object", "properties": {"sku": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "storageGb": {"type": "integer", "format": "int32", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "weightGram": {"type": "number", "format": "double", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "WarehouseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "addressLine": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "WarehouseOptionDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "label": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WarehouseUpsertDto": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "addressLine": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "province": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}