﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PHONE_STORE.Application.Dtos;
using PHONE_STORE.Application.Interfaces;
using System.Security.Claims;

namespace PHONE_STORE.API.Controllers;

[ApiController]
[Route("api/shop/checkout")]
[Authorize]
public class ShopCheckoutController : ControllerBase
{
    private readonly IOrderService _orders;
    public ShopCheckoutController(IOrderService orders) => _orders = orders;

    [HttpPost("preview")]
    public Task<CheckoutPreviewResult> Preview([FromBody] CheckoutPreviewRequest req, CancellationToken ct)
        => _orders.PreviewAsync(long.Parse(User.FindFirst("sub")!.Value), req.AddressId, ct);

    [HttpPost("submit")]
    public async Task<IActionResult> Submit([FromBody] CheckoutSubmitRequest req, CancellationToken ct)
    {
        var me = long.Parse(User.FindFirst("sub")!.Value);
        var id = await _orders.SubmitAsync(me, req.AddressId, req.Note, ct);
        if (id is null) return BadRequest(new { message = "Đơn rỗng hoặc không hợp lệ" });
        return Ok(new { id });
    }
}
