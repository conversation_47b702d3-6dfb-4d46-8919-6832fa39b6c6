﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Serilog;
using PHONE_STORE.WEB.Infrastructure; // JwtCookieHandler

var logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/web-.log", rollingInterval: RollingInterval.Day, retainedFileCountLimit: 7)
    .CreateLogger();
Log.Logger = logger;

try
{
    var builder = WebApplication.CreateBuilder(args);

    builder.Host.UseSerilog((ctx, lc) =>
        lc.ReadFrom.Configuration(ctx.Configuration)
          .Enrich.FromLogContext()
          .Enrich.WithProperty("Service", "PhoneStore.Web"));

    builder.Services.AddControllersWithViews();

    // Cookie auth cho user đăng nhập
    builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
        .AddCookie(o =>
        {
            o.LoginPath = "/Account/Login";
            o.LogoutPath = "/Account/Logout";
            o.ExpireTimeSpan = TimeSpan.FromHours(1);
            o.SlidingExpiration = true;
            o.Cookie.Name = "ps_auth";
            o.Cookie.HttpOnly = true;
            o.Cookie.SameSite = SameSiteMode.Lax;
            o.Cookie.SecurePolicy = builder.Environment.IsDevelopment()
                ? CookieSecurePolicy.SameAsRequest
                : CookieSecurePolicy.Always;
        });

    builder.Services.AddAuthorization();

    var apiBase = builder.Configuration["ApiBaseUrl"] ?? "https://localhost:7277";

    // HttpClient + JwtCookieHandler
    builder.Services.AddHttpContextAccessor();
    builder.Services.AddTransient<JwtCookieHandler>();

    builder.Services.AddHttpClient("api", c =>
    {
        c.BaseAddress = new Uri(apiBase.TrimEnd('/') + "/");
    })
    .AddHttpMessageHandler<JwtCookieHandler>();

    // Client không auth (nếu cần)
    builder.Services.AddHttpClient("ApiNoAuth", c =>
    {
        c.BaseAddress = new Uri(apiBase.TrimEnd('/') + "/");
    });

    var app = builder.Build();

    // middleware phát cookie 'sid' cho khách (để API nhận diện giỏ hàng chưa login)
    app.Use(async (ctx, next) =>
    {
        if (!ctx.Request.Cookies.TryGetValue("sid", out var sid) || string.IsNullOrWhiteSpace(sid))
        {
            sid = Guid.NewGuid().ToString("N");
            ctx.Response.Cookies.Append("sid", sid, new CookieOptions
            {
                HttpOnly = false,
                Secure = !app.Environment.IsDevelopment(), // false trong development
                SameSite = SameSiteMode.Lax,
                Expires = DateTimeOffset.UtcNow.AddMonths(2)
            });
        }
        await next();
    });

    if (!app.Environment.IsDevelopment())
    {
        app.UseExceptionHandler("/Home/Error");
        app.UseHsts();
    }

    app.UseHttpsRedirection();
    app.UseStaticFiles();

    app.UseRouting();

    app.UseAuthentication();
    app.UseAuthorization();

    app.UseSerilogRequestLogging();

    app.MapControllerRoute(
        name: "areas",
        pattern: "{area:exists}/{controller=Home}/{action=Index}/{id?}");

    app.MapControllerRoute(
        name: "default",
        pattern: "{controller=Home}/{action=Index}/{id?}");

    app.MapControllerRoute(
        name: "product_slug",
        pattern: "p/{slug}",
        defaults: new { controller = "Catalog", action = "Details" });

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "WEB terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
