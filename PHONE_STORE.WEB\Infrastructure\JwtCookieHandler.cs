﻿using System.Net.Http.Headers;

namespace PHONE_STORE.WEB.Infrastructure
{
    public class JwtCookieHandler : DelegatingHandler
    {
        private readonly IHttpContextAccessor _http;
        public JwtCookieHandler(IHttpContextAccessor http) => _http = http;

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken ct)
        {
            var ctx = _http.HttpContext;
            if (ctx != null)
            {
                if (ctx.Request.Cookies.TryGetValue("access_token", out var at) && !string.IsNullOrWhiteSpace(at))
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", at);

                if (!request.Headers.Contains("X-Session-Id") &&
                    ctx.Request.Cookies.TryGetValue("sid", out var sid) && !string.IsNullOrWhiteSpace(sid))
                    request.Headers.Add("X-Session-Id", sid);
            }
            return base.SendAsync(request, ct);
        }
    }
}
