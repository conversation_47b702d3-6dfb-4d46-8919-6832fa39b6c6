﻿@model CheckoutPageVm
@{
    ViewData["Title"] = "Thanh toán";
    var err = TempData["Err"] as string;
}
<h1><PERSON>h toán</h1>

@if (!string.IsNullOrWhiteSpace(err))
{
    <div class="alert alert-danger">@err</div>
}

<div class="row">
    <div class="col-md-8">
        <div class="card mb-3">
            <div class="card-header">Địa chỉ giao hàng</div>
            <div class="card-body">
                @if (Model.Addresses.Count == 0)
                {
                    <div class="alert alert-warning">
                        Bạn chưa có địa chỉ. Hãy vào mục <em>Customers</em> (admin) thêm địa chỉ cho tài khoản này, hoặc tạo màn "Địa chỉ của tôi" riêng phía client.
                    </div>
                }
                else
                {
                    <form asp-action="PlaceOrder" method="post">
                        @Html.AntiForgeryToken()
                        <div class="vstack gap-2">
                            @foreach (var a in Model.Addresses)
                            {
                                <label class="border rounded p-2">
                                    <input type="radio" name="addressId" value="@a.Id" @(a.Id == Model.SelectedAddressId ? "checked" : "") />
                                    <strong>@a.Recipient</strong> - @a.Phone<br />
                                    @a.Line1, @a.Ward, @a.District, @a.Province (@a.AddressType)
                                    @if (a.IsDefault)
                                    {
                                        <span class="badge bg-success ms-2">Mặc định</span>
                                    }
                                </label>
                            }
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary">Đặt hàng (COD)</button>
                        </div>
                    </form>
                }
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">Tóm tắt</div>
            <div class="card-body">
                <div class="d-flex justify-content-between"><span>Tạm tính</span><span>@string.Format("{0:N0} ₫", Model.Preview.Subtotal)</span></div>
                <div class="d-flex justify-content-between"><span>VAT</span><span>@string.Format("{0:N0} ₫", Model.Preview.TaxTotal)</span></div>
                <div class="d-flex justify-content-between"><span>Phí vận chuyển</span><span>@string.Format("{0:N0} ₫", Model.Preview.ShippingFee)</span></div>
                <hr />
                <div class="d-flex justify-content-between fw-bold"><span>Tổng cộng</span><span>@string.Format("{0:N0} ₫", Model.Preview.GrandTotal)</span></div>
            </div>
        </div>
    </div>
</div>
