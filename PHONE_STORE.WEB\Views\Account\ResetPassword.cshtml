﻿@{
    ViewData["Title"] = "Đặt lại mật khẩu";
    Layout = "_Layout";
}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-sm-10 col-md-8 col-lg-6">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-body p-4 p-md-5">
                    <h3 class="mb-3 text-center">Đặt lại mật khẩu</h3>
                    @if (TempData["msg"] != null)
                    {
                        <div class="alert alert-success">@TempData["msg"]</div>
                    }
                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger">@Html.ValidationSummary()</div>
                    }

                    <form method="post" asp-antiforgery="true" asp-action="ResetPassword" novalidate>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email đã đăng ký</label>
                            <input type="email" class="form-control" id="email" name="email" autocomplete="email" required />
                        </div>
                        <div class="mb-3">
                            <label for="otp" class="form-label">Mã OTP</label>
                            <input type="text" class="form-control" id="otp" name="otp" inputmode="numeric" pattern="[0-9]*" required />
                        </div>
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="newPassword" name="newPassword" autocomplete="new-password" required />
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Đổi mật khẩu</button>

                        <div class="text-center mt-3">
                            <a asp-action="ForgotPassword">Chưa nhận OTP? Gửi lại</a>
                            <span class="mx-2">·</span>
                            <a asp-action="Login">Đăng nhập</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
