{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": { "Oracle": "User Id=****;Password=****;Data Source=HuongNe:1522/xepdb1;Pooling=true" },
  "Cors": { "AllowedOrigins": [ "https://localhost:7082", "https://localhost:7277" ] },
  "Jwt": {
    "Issuer": "PhoneStore.Api",
    "Audience": "PhoneStore.Web",
    "AccessTokenMinutes": 15,
    "ActiveKid": "default",
    "Keys": [
      {
        "Kid": "default",
        "Key": "your-super-secret-key-at-least-32-characters-long-for-security-purposes"
      }
    ]
  },
  "Redis": {
    "Configuration": "localhost:6379",
    "InstanceName": "ps:"
  },
  "LoginLock": {
    "WindowMinutes": 10,
    "MaxFails": 5
  },
  "Email": {
    "Smtp": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "EnableSsl": true,
      "Username": "<EMAIL>",
      "Password": "kxbp knlx cnui mpnx",
      "FromEmail": "<EMAIL>",
      "FromName": "Phone Store"
    }
  }

}
//File này chính là cấu hình app cho API:

//Logging mức nào.

//Kết nối DB Oracle.

//CORS cho web client.

//JWT config (issuer, audience, lifetime).

//Redis config (để lưu refresh token, login fail lock).