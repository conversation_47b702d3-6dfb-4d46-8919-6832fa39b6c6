﻿@model PHONE_STORE.Application.Dtos.AttributeCreateDto
@{
    ViewData["Title"] = "Create attribute";
}
<h2>Create Attribute</h2>
@if (TempData["Error"] != null)
{
    <div class="alert alert-danger">@TempData["Error"]</div>
}
<form method="post" asp-area="Admin" asp-controller="Attributes" asp-action="Create">
    @Html.AntiForgeryToken()
    <div class="mb-3"><label class="form-label">Code</label><input class="form-control" asp-for="Code" /></div>
    <div class="mb-3"><label class="form-label">Name</label><input class="form-control" asp-for="Name" /></div>
    <div class="mb-3">
        <label class="form-label">Data Type</label>
        <select class="form-select" asp-for="DataType">
            <option>TEXT</option>
            <option>INT</option>
            <option>DECIMAL</option>
            <option>BOOL</option>
        </select>
    </div>
    <button class="btn btn-primary">Save</button>
    <a class="btn btn-secondary" asp-area="Admin" asp-controller="Attributes" asp-action="Index">Back</a>
</form>
