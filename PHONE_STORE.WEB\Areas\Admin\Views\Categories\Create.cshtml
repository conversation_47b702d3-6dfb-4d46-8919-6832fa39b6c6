﻿@model PHONE_STORE.WEB.Models.CategoryFormVm
@{
    ViewData["Title"] = "Create Category";
}
<h1 class="mb-3">Create Category</h1>

<form asp-action="Create" method="post" class="card p-3">
    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

    <div class="mb-3">
        <label asp-for="ParentId" class="form-label"></label>
        <select asp-for="ParentId" class="form-select" asp-items="Model.ParentOptions"></select>



    </div>

    <div class="mb-3">
        <label asp-for="Name" class="form-label"></label>
        <input asp-for="Name" class="form-control" />
        <span asp-validation-for="Name" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="Slug" class="form-label"></label>
        <input asp-for="Slug" class="form-control" placeholder="(để trống: tự sinh từ Name)" />
        <span class="form-text">Bỏ trống để hệ thống tự sinh slug.</span>
    </div>

    <div class="mb-3">
        <label asp-for="SortOrder" class="form-label"></label>
        <input asp-for="SortOrder" type="number" class="form-control" />
    </div>

    <div class="form-check mb-3">
        <input asp-for="IsActive" class="form-check-input" />
        <label asp-for="IsActive" class="form-check-label"></label>
    </div>

    <div class="d-flex gap-2">
        <button class="btn btn-primary" type="submit">Create</button>
        <a asp-area="Admin" asp-controller="Categories" asp-action="Index" class="btn btn-secondary">Back</a>
    </div>
</form>

@section Scripts {
    <partial name="~/Views/Shared/_ValidationScriptsPartial.cshtml" />
}
