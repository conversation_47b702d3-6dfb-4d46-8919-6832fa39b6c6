﻿@{
    ViewData["Title"] = "Đ<PERSON>i mật khẩu";
    Layout = "_Layout";
}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-sm-10 col-md-8 col-lg-6">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-body p-4 p-md-5">
                    <h3 class="mb-3 text-center">Đ<PERSON>i mật khẩu</h3>

                    @if (TempData["msg"] != null)
                    {
                        <div class="alert alert-success">@TempData["msg"]</div>
                    }
                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger">@Html.ValidationSummary()</div>
                    }

                    <form method="post" asp-antiforgery="true" asp-action="ChangePassword" novalidate>
                        <div class="mb-3">
                            <label for="oldPassword" class="form-label">M<PERSON><PERSON> khẩu hiện tại</label>
                            <input type="password" class="form-control" id="oldPassword" name="oldPassword" autocomplete="current-password" required />
                        </div>
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="newPassword" name="newPassword" autocomplete="new-password" required />
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Nhập lại mật khẩu mới</label>
                            <input type="password" class="form-control" id="confirmPassword" autocomplete="new-password" required />
                            <div class="form-text">Mật khẩu mới phải trùng khớp.</div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Cập nhật</button>
                        <div class="text-center mt-3">
                            <a asp-action="Login">Đăng nhập lại</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // kiểm tra confirm phía client
        (function () {
            const form = document.forms[0];
            form?.addEventListener('submit', function (e) {
                const p1 = document.getElementById('newPassword')?.value || '';
                const p2 = document.getElementById('confirmPassword')?.value || '';
                if (p1 !== p2) {
                    e.preventDefault();
                    alert('Mật khẩu xác nhận không khớp.');
                }
            });
        })();
    </script>
}
