﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Oracle.EntityFrameworkCore.Metadata;
using PHONE_STORE.Infrastructure.Data;

#nullable disable

namespace PHONE_STORE.Infrastructure.Migrations
{
    [DbContext(typeof(PhoneDbContext))]
    [Migration("20250825035038_AddUniqueIndexes")]
    partial class AddUniqueIndexes
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            OracleModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("PHONE_STORE.Infrastructure.Entities.Role", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(19)")
                        .HasColumnName("ID");

                    OraclePropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("CODE");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("NAME");

                    b.HasKey("Id");

                    b.ToTable("ROLES", (string)null);
                });

            modelBuilder.Entity("PHONE_STORE.Infrastructure.Entities.UserAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(19)")
                        .HasColumnName("ID");

                    OraclePropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CREATED_AT");

                    b.Property<string>("Email")
                        .HasColumnType("NVARCHAR2(450)")
                        .HasColumnName("EMAIL");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("PASSWORD_HASH");

                    b.Property<string>("Phone")
                        .HasColumnType("NVARCHAR2(450)")
                        .HasColumnName("PHONE");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("STATUS");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("UX_USER_ACCOUNTS_EMAIL")
                        .HasFilter("\"EMAIL\" IS NOT NULL");

                    b.HasIndex("Phone")
                        .IsUnique()
                        .HasDatabaseName("UX_USER_ACCOUNTS_PHONE")
                        .HasFilter("\"PHONE\" IS NOT NULL");

                    b.ToTable("USER_ACCOUNTS", (string)null);
                });

            modelBuilder.Entity("PHONE_STORE.Infrastructure.Entities.UserRole", b =>
                {
                    b.Property<long>("UserId")
                        .HasColumnType("NUMBER(19)")
                        .HasColumnName("USER_ID");

                    b.Property<long>("RoleId")
                        .HasColumnType("NUMBER(19)")
                        .HasColumnName("ROLE_ID");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_USER_ROLES_ROLE_ID");

                    b.ToTable("USER_ROLES", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
