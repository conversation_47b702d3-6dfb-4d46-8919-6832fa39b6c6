﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PHONE_STORE.Application.Dtos;
using PHONE_STORE.Application.Interfaces;

namespace PHONE_STORE.API.Controllers;

[ApiController]
[Route("api/shop/cart")]
public class ShopCartController : ControllerBase
{
    private readonly ICartRepository _repo;
    public ShopCartController(ICartRepository repo) => _repo = repo;

    string? SessionId =>
        Request.Headers["X-Session-Id"].FirstOrDefault()
        ?? Request.Cookies["sid"];

    long? CustomerId => long.TryParse(User.FindFirst("sub")?.Value ?? "", out var id) ? id : null;

    [HttpGet]
    public Task<CartDto> Get(CancellationToken ct)
        => _repo.GetOrCreateAsync(CustomerId, SessionId, ct);

    [HttpPost("items")]
    public Task<CartDto> Add([FromBody] CartItemUpsertDto dto, CancellationToken ct)
        => _repo.AddOrUpdateItemAsync(CustomerId, SessionId, dto, ct);

    [HttpPut("items/{itemId:long}")]
    public Task<CartDto> Update(long itemId, [FromQuery] int qty, CancellationToken ct)
        => _repo.UpdateQtyAsync(itemId, qty, CustomerId, SessionId, ct);

    [HttpDelete("items/{itemId:long}")]
    public Task Remove(long itemId, CancellationToken ct)
        => _repo.RemoveItemAsync(itemId, CustomerId, SessionId, ct);

    // 🔥 Thêm: merge giỏ guest (sid) vào giỏ của user khi đã đăng nhập
    [Authorize]
    [HttpPost("merge")]
    public async Task<IActionResult> Merge(CancellationToken ct)
    {
        var sid = SessionId;
        if (string.IsNullOrWhiteSpace(sid)) return Ok();
        var userId = long.Parse(User.FindFirst("sub")!.Value);
        await _repo.MergeAsync(sid, userId, ct);
        return Ok();
    }
}