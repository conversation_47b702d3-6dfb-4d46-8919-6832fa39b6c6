﻿@{
    ViewData["Title"] = "Quên mật khẩu";
    Layout = "_Layout";
}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-sm-10 col-md-8 col-lg-6">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-body p-4 p-md-5">
                    <h3 class="mb-3 text-center">Quên mật khẩu</h3>

                    @if (TempData["msg"] != null)
                    {
                        <div class="alert alert-info">@TempData["msg"]</div>
                    }

                    <form method="post" asp-antiforgery="true" asp-action="ForgotPassword">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email đã đăng ký</label>
                            <input type="email" class="form-control" id="email" name="email" autocomplete="email" required />
                            <div class="form-text">Chúng tôi sẽ gửi mã OTP nếu email tồn tại.</div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">Gửi mã OTP</button>

                        <div class="text-center mt-3">
                            <a asp-action="ResetPassword">Tôi đã có mã OTP</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
