﻿@model PHONE_STORE.WEB.Models.PriceFormVm
<div class="row">
    <div class="col-md-3 mb-3"><label asp-for="ListPrice" class="form-label"></label><input asp-for="ListPrice" class="form-control" /></div>
    <div class="col-md-3 mb-3"><label asp-for="SalePrice" class="form-label"></label><input asp-for="SalePrice" class="form-control" /></div>
    <div class="col-md-2 mb-3"><label asp-for="Currency" class="form-label"></label><input asp-for="Currency" class="form-control" /></div>
</div>
<div class="row">
    <div class="col-md-4 mb-3"><label asp-for="StartsAt" class="form-label"></label><input asp-for="StartsAt" class="form-control" type="datetime-local" /></div>
    <div class="col-md-4 mb-3"><label asp-for="EndsAt" class="form-label"></label><input asp-for="EndsAt" class="form-control" type="datetime-local" /></div>
</div>
<div>
    <button class="btn btn-primary">Save</button>
    <a class="btn btn-secondary" href="javascript:history.back()">Back</a>
</div>
