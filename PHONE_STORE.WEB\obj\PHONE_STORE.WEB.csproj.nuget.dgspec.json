{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.WEB\\PHONE_STORE.WEB.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\PHONE_STORE.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\PHONE_STORE.Application.csproj", "projectName": "PHONE_STORE.Application", "projectPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\PHONE_STORE.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.1, )"}, "Raiqub.JabModules.MicrosoftExtensionsOptions": {"target": "Package", "version": "[1.0.16, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Infrastructure\\PHONE_STORE.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Infrastructure\\PHONE_STORE.Infrastructure.csproj", "projectName": "PHONE_STORE.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Infrastructure\\PHONE_STORE.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\PHONE_STORE.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\PHONE_STORE.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.8, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[9.23.90, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.9.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.WEB\\PHONE_STORE.WEB.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.WEB\\PHONE_STORE.WEB.csproj", "projectName": "PHONE_STORE.WEB", "projectPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.WEB\\PHONE_STORE.WEB.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.WEB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\PHONE_STORE.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Application\\PHONE_STORE.Application.csproj"}, "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Infrastructure\\PHONE_STORE.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\TongHopKienThuc\\CSharp\\PHONE_STORE\\PHONE_STORE.Infrastructure\\PHONE_STORE.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": {"target": "Package", "version": "[2.3.0, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}