[{"ContainingType": "AttributesController", "Method": "List", "RelativePath": "api/admin/attributes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.AttributeDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "AttributesController", "Method": "Create", "RelativePath": "api/admin/attributes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.AttributeCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AttributesController", "Method": "Update", "RelativePath": "api/admin/attributes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.AttributeUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AttributesController", "Method": "Delete", "RelativePath": "api/admin/attributes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BrandsController", "Method": "Search", "RelativePath": "api/admin/brands", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.PagedResult`1[[PHONE_STORE.Application.Dtos.BrandDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BrandsController", "Method": "Create", "RelativePath": "api/admin/brands", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.BrandCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BrandsController", "Method": "Get", "RelativePath": "api/admin/brands/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.BrandDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BrandsController", "Method": "Update", "RelativePath": "api/admin/brands/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.BrandUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BrandsController", "Method": "Delete", "RelativePath": "api/admin/brands/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BrandsController", "Method": "Options", "RelativePath": "api/admin/brands/options", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.IdNameDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CategoriesController", "Method": "Search", "RelativePath": "api/admin/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.PagedResult`1[[PHONE_STORE.Application.Dtos.CategoryDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CategoriesController", "Method": "Create", "RelativePath": "api/admin/categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.CategoryCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CategoriesController", "Method": "Get", "RelativePath": "api/admin/categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.CategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CategoriesController", "Method": "Update", "RelativePath": "api/admin/categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.CategoryUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CategoriesController", "Method": "Delete", "RelativePath": "api/admin/categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CategoriesController", "Method": "Options", "RelativePath": "api/admin/categories/options", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "excludeId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "Search", "RelativePath": "api/admin/customers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "top", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.CustomerDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "Create", "RelativePath": "api/admin/customers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.CustomerUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "Get", "RelativePath": "api/admin/customers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.CustomerDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "Update", "RelativePath": "api/admin/customers/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.CustomerUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "ListAddresses", "RelativePath": "api/admin/customers/{id}/addresses", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.AddressDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/admin/customers/{id}/addresses", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.AddressUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "GetAddress", "RelativePath": "api/admin/customers/{id}/addresses/{addressId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "addressId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.AddressDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "Update<PERSON><PERSON><PERSON>", "RelativePath": "api/admin/customers/{id}/addresses/{addressId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "addressId", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.AddressUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "DeleteAddress", "RelativePath": "api/admin/customers/{id}/addresses/{addressId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "addressId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.CustomersController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/admin/customers/{id}/addresses/{addressId}/set-default", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "addressId", "Type": "System.Int64", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.DeviceUnitsController", "Method": "Search", "RelativePath": "api/admin/device-units", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imei", "Type": "System.String", "IsRequired": false}, {"Name": "variantId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "warehouseId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "top", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.DeviceUnitDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.DeviceUnitsController", "Method": "Get", "RelativePath": "api/admin/device-units/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.DeviceUnitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ImagesController", "Method": "Create", "RelativePath": "api/admin/images", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.ImageCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ImagesController", "Method": "Delete", "RelativePath": "api/admin/images/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ImagesController", "Method": "ForProduct", "RelativePath": "api/admin/images/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.ImageDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ImagesController", "Method": "ForVariant", "RelativePath": "api/admin/images/variant/{variantId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "variantId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.ImageDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.InventoryController", "Method": "Get", "RelativePath": "api/admin/inventory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "variantId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "warehouseId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.InventoryController", "Method": "Move", "RelativePath": "api/admin/inventory/move", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.Application.Dtos.StockMoveRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.OrdersController", "Method": "Search", "RelativePath": "api/admin/orders", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "top", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.OrderListItemDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.OrdersController", "Method": "Get", "RelativePath": "api/admin/orders/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.OrderDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.OrdersController", "Method": "Cancel", "RelativePath": "api/admin/orders/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.OrdersController", "Method": "MarkPaid", "RelativePath": "api/admin/orders/{id}/mark-paid", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.OrdersController", "Method": "Ship", "RelativePath": "api/admin/orders/{id}/ship", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ProductsController", "Method": "Search", "RelativePath": "api/admin/products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "brandId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.PagedResult`1[[PHONE_STORE.Application.Dtos.ProductListItemDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ProductsController", "Method": "Create", "RelativePath": "api/admin/products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.ProductCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ProductsController", "Method": "Get", "RelativePath": "api/admin/products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.ProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ProductsController", "Method": "Update", "RelativePath": "api/admin/products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.ProductUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ProductsController", "Method": "Delete", "RelativePath": "api/admin/products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ProductAttributesController", "Method": "List", "RelativePath": "api/admin/products/{productId}/attributes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ProductAttributesController+ProductAttrValueDto, PHONE_STORE.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ProductAttributesController", "Method": "Upsert", "RelativePath": "api/admin/products/{productId}/attributes/upsert", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.AttributeValueUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.VariantsController", "Method": "List", "RelativePath": "api/admin/products/{productId}/variants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.VariantDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.VariantsController", "Method": "Create", "RelativePath": "api/admin/products/{productId}/variants", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.VariantCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.VariantsController", "Method": "Get", "RelativePath": "api/admin/products/{productId}/variants/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "productId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.VariantDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.VariantsController", "Method": "Update", "RelativePath": "api/admin/products/{productId}/variants/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.VariantUpdateDto", "IsRequired": true}, {"Name": "productId", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.VariantsController", "Method": "Delete", "RelativePath": "api/admin/products/{productId}/variants/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "productId", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.PricesController", "Method": "List", "RelativePath": "api/admin/variants/{variantId}/prices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "variantId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.PriceDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.PricesController", "Method": "Create", "RelativePath": "api/admin/variants/{variantId}/prices", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "variantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.PriceUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.PricesController", "Method": "Update", "RelativePath": "api/admin/variants/{variantId}/prices/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.PriceUpsertDto", "IsRequired": true}, {"Name": "variantId", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.PricesController", "Method": "Delete", "RelativePath": "api/admin/variants/{variantId}/prices/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "variantId", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.PricesController", "Method": "Active", "RelativePath": "api/admin/variants/{variantId}/prices/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "variantId", "Type": "System.Int64", "IsRequired": true}, {"Name": "at", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.PriceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.WarehousesController", "Method": "GetAll", "RelativePath": "api/admin/warehouses", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.WarehouseDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.WarehousesController", "Method": "Create", "RelativePath": "api/admin/warehouses", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.WarehouseUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.WarehousesController", "Method": "Get", "RelativePath": "api/admin/warehouses/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.WarehouseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.WarehousesController", "Method": "Update", "RelativePath": "api/admin/warehouses/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}, {"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.WarehouseUpsertDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.WarehousesController", "Method": "Options", "RelativePath": "api/admin/warehouses/options", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.WarehouseOptionDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.API.Controllers.AuthController+ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/Auth/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.API.Controllers.AuthController+ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.API.Controllers.AuthController+LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.API.Controllers.AuthController+RefreshRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "Me", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "Refresh", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.API.Controllers.AuthController+RefreshRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.API.Controllers.AuthController+RegisterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.API.Controllers.AuthController+ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopCartController", "Method": "Get", "RelativePath": "api/shop/cart", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.CartDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopCartController", "Method": "Add", "RelativePath": "api/shop/cart/items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PHONE_STORE.Application.Dtos.CartItemUpsertDto", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.CartDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopCartController", "Method": "Update", "RelativePath": "api/shop/cart/items/{itemId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int64", "IsRequired": true}, {"Name": "qty", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.CartDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopCartController", "Method": "Remove", "RelativePath": "api/shop/cart/items/{itemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopCartController", "Method": "<PERSON><PERSON>", "RelativePath": "api/shop/cart/merge", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopCheckoutController", "Method": "Preview", "RelativePath": "api/shop/checkout/preview", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.Application.Dtos.CheckoutPreviewRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.Application.Dtos.CheckoutPreviewResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopCheckoutController", "Method": "Submit", "RelativePath": "api/shop/checkout/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "req", "Type": "PHONE_STORE.Application.Dtos.CheckoutSubmitRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopMeController", "Method": "MyAdd<PERSON>", "RelativePath": "api/shop/me/addresses", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.Application.Dtos.AddressDto, PHONE_STORE.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopProductsController", "Method": "List", "RelativePath": "api/shop/products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "q", "Type": "System.String", "IsRequired": false}, {"Name": "catId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[PHONE_STORE.API.Controllers.ShopProductsController+ShopProductListItem, PHONE_STORE.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.ShopProductsController", "Method": "Details", "RelativePath": "api/shop/products/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "PHONE_STORE.API.Controllers.ShopProductsController+ShopProductDetail", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PHONE_STORE.API.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[PHONE_STORE.API.WeatherForecast, PHONE_STORE.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]